@import theme(editorMenu/menu);

.container {
	position: fixed;
	left: var(--editor-menu-container-poistion-left);
	top: auto;
	width: calc(100% - var(--editor-menu-container-poistion-left));
	background: var(--editor-menu-container-background);
	padding: var(--editor-menu-container-padding);
	z-index: 125;
	box-sizing: border-box;
	clip: var(--editor-menu-container-clip-mask);
	height: 60px;
	transition: all 0.5s ease;

	@apply --editor-menu-container;

	@media (--screen-mobile) {
		top: var(--editor-menu-container-position-top-mobile);
		left: var(--editor-menu-container-poistion-left-mobile);
		width: calc(100% - var(--editor-menu-container-poistion-left-mobile));
	}

	& .editorMenuButton {
		& i {
			font-family: var(--editor-menu-font-bold);
			font-size: var(--editor-menu-small-font-size);
			line-height: var(--editor-menu-small-line-height);
			color: var(--editor-menu-small-color, #FFFFFF);
			font-style: normal;
			display: inline-block;
			vertical-align: var(--editor-menu-button-text-vertical-align);
			margin-right: var(--editor-menu-button-text-margin-right);
		}

		& .dotContainer {
			transform: translateX(44px);
		}
	}

	& .editorMenuButton,
	& .editorMenuBackButton {
		border: 0;
		padding: 0;
		box-sizing: border-box;
		display: block;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: var(--editor-menu-button-position-right);
		vertical-align: middle;
		cursor: pointer;
		margin: 0;
		z-index: 10;

		@media (--screen-mobile) {
			right: var(--editor-menu-button-position-right-mobile);
		}

		@apply --editor-menu-publish-button-containers;

		& .dotContainer {
			width: var(--editor-menu-button-width);
			height: var(--editor-menu-button-height);
			border-radius: 100%;
			background: var(--editor-menu-button-background-image) var(--editor-menu-background-color) var(--editor-menu-button-background-size) no-repeat;
			border: var(--editor-menu-button-border);
			padding: var(--editor-menu-button-padding);
			vertical-align: middle;
			background-position: center;
			display: block;
			box-sizing: border-box;
			transition: all 0.5s ease;

			&:hover {
				background: var(--editor-menu-button-background-image) var(--editor-menu-background-color-hover) var(--editor-menu-button-background-size) no-repeat;
				background-position: center;
			}

			@apply --editor-menu-dot-container;
		}
	}

	& .editorMenuBackButton {
		width: calc(var(--editor-menu-button-width) + 2px);
		height: calc(var(--editor-menu-button-height) + 2px);
		border: solid 1px transparent;
		border-radius: 100%;
		z-index: 15;

		& .dotContainer {
			background: var(--editor-menu-button-back-background-image) var(--editor-menu-background-color) no-repeat var(--editor-menu-button-background-size);
			background-position: center;
			padding: 0;

			&:hover {
				background: var(--editor-menu-button-back-background-image) var(--editor-menu-background-color-hover) no-repeat var(--editor-menu-button-background-size);
				background-position: center;
			}
		}

		&.editorMenuBackButtonHidden {
			visibility: hidden;
		}
	}

	&.editorMenuOpen {
		clip: var(--editor-menu-container-clip-mask-open);

		& ul {
			padding: 10px;
		}

		& .editorMenu,
		& .editorMenuDialog {
			border: var(--editor-menu-border);
			transform: translateY(6px);

			@media (--screen-mobile) {
				width: calc(100% - 62px);
			}

			&:before {
				opacity: 1;
			}

			& .editorMenuItem {
				&:last-child:not(:only-child) {
					border-top: var(--editor-menu-item-last-child-border);
				}
			}
		}

		& .editorMenuIndicator {
			transform: rotate(180deg);
		}
	}

	&.editorMenuClosed {
		& .editorMenu,
		& .editorMenuDialog {
			border-color: transparent;
			background: transparent;
			transition-delay: 0.3s;
			pointer-events: none;
			box-shadow: 0 0 25px -15px color(var(--black) alpha(-100%));
			opacity: 0;

			&:before {
				transition-delay: 0s;
			}

			& .editorMenuItem {
				&:last-child {
					border-top: solid 1px transparent;
				}
			}
		}

		& .editorMenuDialog {
			& > div {
				opacity: 0;
			}
		}

		& .editorMenuIndicator {
			transform: rotate(0);
		}
	}

	& .editorMenu,
	& .editorMenuDialog {
		position: absolute;
		top: var(--editor-menu-position-top);
		right: var(--editor-menu-dialog-position-right);
		padding: 0;
		margin: 14px 0 0 0;
		min-width: 170px;
		background: var(--white);
		border-radius: 4px;
		border: 1px solid transparent;
		z-index: 1;
		transform: translateY(-20px);
		transition: all 0.5s ease;
		box-shadow: 0 5px 25px -5px color(var(--black) alpha(-50%));
		opacity: 1;

		@media (--screen-mobile) {
			right: var(--editor-menu-dialog-position-right-mobile);
			width: calc(100% - 42px);
		}

		&:before {
			content: url(../../clients/common/assets/img/icons/arrowpopup.svg);
			display: block;
			width: 10px;
			height: 10px;
			position: absolute;
			top: 0;
			right: var(--editor-menu-button-arrow-position-right);
			transform: translateY(var(--editor-menu-button-arrow-translate-y));
			opacity: 0;
			transition: all 0.5s ease;
			transition-delay: 0.3s;
			line-height: var(--editor-menu-button-arrow-line-height);

			@media (--screen-mobile) {
				right: var(--editor-menu-button-arrow-position-right-mobile);
			}
		}

		& .editorMenuIndicator {
			font-size: 27px;
			display: block;
			text-align: center;
			transition: all 0.5s ease;
		}

		& .editorMenuItem {
			padding: 10px 0;
			margin: 0;
			list-style-type: none;
			display: block;
			transition: all 0.5s ease;

			&:before,
			&:after {
				display: none;
				content: none;
			}

			&:last-child:not(:only-child) {
				border-top: solid 1px transparent;
				padding-top: var(--editor-menu-item-padding-top);
				margin-top: var(--editor-menu-item-margin-top);
			}

			& button {
				border: 0;
				background: none;
				color: var(--editor-menu-item-color);
				font-size: var(--editor-menu-small-font-size);
				line-height: var(--editor-menu-small-line-height);
				font-family: var(--editor-menu-font-bold);
				margin: 0;
				font-weight: normal;
				height: auto;
				text-align: left;
				cursor: pointer;
				box-sizing: border-box;
				padding: 0 10px;
				width: 100%;
				transition: all 0.3s ease;

				&:hover:not(:disabled) {
					color: var(--editor-menu-item-color-hover);
				}

				&:disabled {
					opacity: 0.7;
					cursor: not-allowed;
				}
			}
		}
	}

	& .editorMenuDialog {
		/* as it's absolutely positioned, there's a chance that the submit buttons would be off the bottom of the view port */
		@media (max-height: 1212px) {
			overflow: auto;
			height: 900px;
		}
		@media (max-height: 800px) {
			overflow: auto;
		}
	}
}
