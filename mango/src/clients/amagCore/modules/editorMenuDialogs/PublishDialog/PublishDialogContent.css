@import theme(editorMenu/publishDialog);

.content {
	overflow: auto;
	box-sizing: border-box;
	padding: 20px;
	transition: all 0.5s ease;
	margin: 30px 0;
	height: 900px;
}

.opened {
	display: block;
	height: 900px;
}

.closed {
	display: none;
}

.label {
	color: var(--editorMenu-publishDialog-labelColor);
	font-family: var(--editorMenu-publishDialog-fontFamily);
	font-size: var(--editorMenu-publishDialog-fontSize);
	line-height: var(--editorMenu-publishDialog-lineHeight);
}

.backButton,
.backButton:hover {
	all: initial;
	display: inline-block;
	margin: 10px 3px 0 3px;
	padding: 10px 0;
	cursor: pointer;
	font-family: inherit;
	font-size: inherit;
	@apply --editorMenu-publishDialog-backButton;
}
